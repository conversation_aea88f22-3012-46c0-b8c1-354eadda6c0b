import hashlib
import secrets
import json
import os
from datetime import datetime, timedelta
from functools import wraps
from flask import session, request, jsonify, redirect, url_for

# 导入北京时间工具
from beijing_time import beijing_now_iso

class UserManager:
    def __init__(self, data_file='users.json'):
        self.data_file = data_file
        self.users = self.load_users()
    
    def load_users(self):
        """加载用户数据"""
        if os.path.exists(self.data_file):
            try:
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except (json.JSONDecodeError, IOError):
                return {}
        return {}
    
    def save_users(self):
        """保存用户数据"""
        try:
            # 创建备份
            if os.path.exists(self.data_file):
                backup_file = f"{self.data_file}.backup"
                with open(self.data_file, 'r', encoding='utf-8') as src:
                    with open(backup_file, 'w', encoding='utf-8') as dst:
                        dst.write(src.read())
            
            # 保存新数据
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(self.users, f, ensure_ascii=False, indent=2)
            return True
        except IOError:
            return False
    
    def hash_password(self, password):
        """密码加密"""
        salt = secrets.token_hex(16)
        password_hash = hashlib.pbkdf2_hmac('sha256', 
                                          password.encode('utf-8'), 
                                          salt.encode('utf-8'), 
                                          100000)
        return salt + password_hash.hex()
    
    def verify_password(self, password, hashed_password):
        """验证密码"""
        salt = hashed_password[:32]
        stored_hash = hashed_password[32:]
        password_hash = hashlib.pbkdf2_hmac('sha256',
                                          password.encode('utf-8'),
                                          salt.encode('utf-8'),
                                          100000)
        return password_hash.hex() == stored_hash
    
    def register_user(self, username, password, email=None, require_approval=False):
        """用户注册"""
        if username in self.users:
            return False, "用户名已存在"

        if len(username) < 3:
            return False, "用户名至少需要3个字符"

        if len(password) < 6:
            return False, "密码至少需要6个字符"

        # 根据系统设置决定用户状态
        if require_approval:
            user_status = 'pending'  # 待审核
            success_message = "注册成功，请等待管理员审核"
            initial_points = 0  # 待审核用户暂不赠送积分
        else:
            user_status = 'approved'  # 直接通过
            success_message = "注册成功，赠送10点积分"
            initial_points = 10  # 新用户赠送10点积分

        # 创建新用户
        user_data = {
            'username': username,
            'password': self.hash_password(password),
            'email': email or '',
            'points': initial_points,
            'created_at': beijing_now_iso(),
            'last_login': None,
            'is_admin': False,
            'total_generated': 0,  # 总生成次数
            'generation_history': [],  # 生成历史
            'status': user_status,  # 用户状态：pending, approved, rejected
            'approved_at': None if require_approval else beijing_now_iso(),  # 审核通过时间
            'approved_by': None  # 审核人
        }

        self.users[username] = user_data

        if self.save_users():
            return True, success_message
        else:
            return False, "注册失败，请重试"

    def create_linux_do_user(self, username, linux_do_id, linux_do_username, name=None, email=None, avatar_template=None):
        """创建Linux Do OAuth2用户"""
        if username in self.users:
            return False

        # 创建新用户数据
        user_data = {
            'username': username,
            'password': None,  # OAuth2用户不需要密码
            'email': email or '',
            'points': 10,  # 新用户赠送10点积分
            'created_at': beijing_now_iso(),
            'last_login': beijing_now_iso(),
            'is_admin': False,
            'total_generated': 0,
            'generation_history': [],
            'status': 'approved',  # OAuth2用户直接通过
            'approved_at': beijing_now_iso(),
            'approved_by': 'system_oauth2',
            # Linux Do 特有字段
            'auth_type': 'linux_do',
            'linux_do_id': linux_do_id,
            'linux_do_username': linux_do_username,
            'linux_do_name': name or '',
            'linux_do_avatar_template': avatar_template or ''
        }

        self.users[username] = user_data
        return self.save_users()
    
    def login_user(self, username, password):
        """用户登录"""
        if username not in self.users:
            return False, "用户名不存在"

        user = self.users[username]
        if not self.verify_password(password, user['password']):
            return False, "密码错误"

        # 检查用户审核状态
        user_status = user.get('status', 'approved')  # 兼容旧数据，默认为已通过
        if user_status == 'pending':
            return False, "您的账户正在等待管理员审核，请耐心等待"
        elif user_status == 'rejected':
            return False, "您的账户审核未通过，请联系管理员"

        # 更新最后登录时间
        user['last_login'] = beijing_now_iso()
        self.save_users()

        return True, "登录成功"
    
    def get_user(self, username):
        """获取用户信息"""
        return self.users.get(username)
    
    def update_user_points(self, username, points_change):
        """更新用户积分"""
        if username not in self.users:
            return False, "用户不存在"
        
        user = self.users[username]
        new_points = user['points'] + points_change
        
        if new_points < 0:
            return False, "积分不足"
        
        user['points'] = new_points
        self.save_users()
        return True, f"积分更新成功，当前积分：{new_points}"
    
    def add_generation_record(self, username, generation_type, prompt, success=True, max_records=50,
                             negative_prompt=None, image_url=None, video_url=None, model_name=None):
        """添加生成记录

        Args:
            username: 用户名
            generation_type: 生成类型 ('image' or 'video')
            prompt: 提示词
            success: 是否成功
            max_records: 最大保留记录数，默认50条
            negative_prompt: 负面提示词
            image_url: 图片URL
            video_url: 视频URL
            model_name: 使用的模型名称
        """
        if username not in self.users:
            return False

        user = self.users[username]
        record = {
            'type': generation_type,  # 'image' or 'video'
            'prompt': prompt[:200],  # 增加到200个字符以保存更多信息
            'negative_prompt': negative_prompt[:200] if negative_prompt else '',  # 保存负面提示词
            'timestamp': beijing_now_iso(),
            'success': success,
            'username': username,  # 添加用户名字段
            'model_name': model_name or '',  # 使用的模型名称
            'image_url': image_url if generation_type == 'image' and success else None,  # 图片URL
            'video_url': video_url if generation_type == 'video' and success else None   # 视频URL
        }

        user['generation_history'].append(record)

        # 优化：只保留最近的指定条数记录，减少存储空间
        if len(user['generation_history']) > max_records:
            user['generation_history'] = user['generation_history'][-max_records:]

        # 定期清理过期记录（超过30天的记录）
        self._cleanup_old_records(user, days_to_keep=30)

        if success:
            user['total_generated'] += 1

        self.save_users()
        return True

    def _cleanup_old_records(self, user, days_to_keep=30):
        """清理过期的生成记录

        Args:
            user: 用户数据字典
            days_to_keep: 保留天数，默认30天
        """
        if not user.get('generation_history'):
            return

        from datetime import datetime, timedelta
        cutoff_date = datetime.now() - timedelta(days=days_to_keep)

        # 过滤掉过期的记录
        filtered_history = []
        for record in user['generation_history']:
            try:
                # 解析时间戳
                record_time = datetime.fromisoformat(record['timestamp'].replace('Z', '+00:00'))
                if record_time.replace(tzinfo=None) > cutoff_date:
                    filtered_history.append(record)
            except (ValueError, KeyError):
                # 如果时间戳格式有问题，保留记录以避免数据丢失
                filtered_history.append(record)

        user['generation_history'] = filtered_history

    def cleanup_all_users_history(self, max_records=50, days_to_keep=30):
        """批量清理所有用户的历史记录

        Args:
            max_records: 每个用户最大保留记录数
            days_to_keep: 保留天数

        Returns:
            dict: 清理统计信息
        """
        cleaned_users = 0
        total_records_before = 0
        total_records_after = 0

        for username, user in self.users.items():
            if 'generation_history' in user:
                records_before = len(user['generation_history'])
                total_records_before += records_before

                # 清理过期记录
                self._cleanup_old_records(user, days_to_keep)

                # 限制记录数量
                if len(user['generation_history']) > max_records:
                    user['generation_history'] = user['generation_history'][-max_records:]

                records_after = len(user['generation_history'])
                total_records_after += records_after

                if records_before > records_after:
                    cleaned_users += 1

        # 保存清理后的数据
        if cleaned_users > 0:
            self.save_users()

        return {
            'cleaned_users': cleaned_users,
            'total_records_before': total_records_before,
            'total_records_after': total_records_after,
            'records_removed': total_records_before - total_records_after
        }

    def get_user_stats(self, username):
        """获取用户统计信息"""
        if username not in self.users:
            return None

        user = self.users[username]
        recent_history = user['generation_history'][-10:]  # 最近10条记录

        return {
            'username': username,
            'points': user['points'],
            'total_generated': user['total_generated'],
            'created_at': user['created_at'],
            'last_login': user['last_login'],
            'recent_history': recent_history,
            'status': user.get('status', 'approved'),  # 兼容旧数据
            'is_admin': user.get('is_admin', False)
        }

    def get_recent_gallery_records(self, hours=1, limit=50, search=''):
        """获取最近一段时间内所有用户的成功生成记录，用于画廊展示

        Args:
            hours: 获取多少小时内的记录，默认1小时
            limit: 最大返回记录数，默认50条
            search: 搜索关键词，在提示词、用户名中搜索

        Returns:
            list: 按时间倒序排列的生成记录列表
        """
        from datetime import datetime, timedelta

        # 计算时间范围
        cutoff_time = datetime.now() - timedelta(hours=hours)
        gallery_records = []

        # 遍历所有用户的生成历史
        for username, user in self.users.items():
            if 'generation_history' not in user:
                continue

            for record in user['generation_history']:
                # 只显示成功的记录
                if not record.get('success', False):
                    continue

                # 只显示有URL的记录
                if not (record.get('image_url') or record.get('video_url')):
                    continue

                try:
                    # 解析时间戳
                    record_time = datetime.fromisoformat(record['timestamp'].replace('Z', '+00:00'))
                    record_time = record_time.replace(tzinfo=None)

                    # 检查是否在时间范围内
                    if record_time > cutoff_time:
                        # 添加用户名信息（如果记录中没有的话）
                        if 'username' not in record:
                            record['username'] = username

                        # 搜索过滤
                        if search:
                            search_lower = search.lower()
                            # 在提示词、用户名、模型名中搜索
                            prompt = record.get('prompt', '').lower()
                            negative_prompt = record.get('negative_prompt', '').lower()
                            model_name = record.get('model_name', '').lower()
                            username_lower = username.lower()

                            if (search_lower in prompt or
                                search_lower in negative_prompt or
                                search_lower in model_name or
                                search_lower in username_lower):
                                gallery_records.append(record)
                        else:
                            gallery_records.append(record)

                except (ValueError, KeyError):
                    # 时间戳格式有问题，跳过这条记录
                    continue

        # 按时间倒序排列
        gallery_records.sort(key=lambda x: x['timestamp'], reverse=True)

        # 限制返回数量
        return gallery_records[:limit]

    def approve_user(self, username, admin_username):
        """管理员审核通过用户"""
        if username not in self.users:
            return False, "用户不存在"

        user = self.users[username]
        if user.get('status') != 'pending':
            return False, "用户不在待审核状态"

        # 更新用户状态
        user['status'] = 'approved'
        user['approved_at'] = beijing_now_iso()
        user['approved_by'] = admin_username
        user['points'] = 10  # 审核通过后赠送积分

        if self.save_users():
            return True, "用户审核通过，已赠送10点积分"
        else:
            return False, "审核操作失败"

    def reject_user(self, username, admin_username, reason=""):
        """管理员拒绝用户"""
        if username not in self.users:
            return False, "用户不存在"

        user = self.users[username]
        if user.get('status') != 'pending':
            return False, "用户不在待审核状态"

        # 更新用户状态
        user['status'] = 'rejected'
        user['rejected_at'] = beijing_now_iso()
        user['rejected_by'] = admin_username
        user['reject_reason'] = reason

        if self.save_users():
            return True, "用户审核已拒绝"
        else:
            return False, "审核操作失败"

    def get_pending_users(self):
        """获取待审核用户列表"""
        pending_users = []
        for username, user_data in self.users.items():
            if user_data.get('status') == 'pending':
                pending_users.append({
                    'username': username,
                    'email': user_data.get('email', ''),
                    'created_at': user_data.get('created_at', ''),
                    'status': user_data.get('status', 'pending')
                })

        # 按注册时间排序，最新的在前
        pending_users.sort(key=lambda x: x['created_at'], reverse=True)
        return pending_users

    def reset_user_password(self, username, new_password, admin_username):
        """管理员重置用户密码"""
        if username not in self.users:
            return False, "用户不存在"

        if len(new_password) < 6:
            return False, "密码至少需要6个字符"

        # 更新用户密码
        self.users[username]['password'] = self.hash_password(new_password)
        self.users[username]['password_reset_at'] = beijing_now_iso()
        self.users[username]['password_reset_by'] = admin_username

        if self.save_users():
            return True, f"用户 {username} 的密码已重置"
        else:
            return False, "密码重置失败"

    def delete_user(self, username, admin_username):
        """管理员删除用户"""
        if username not in self.users:
            return False, "用户不存在"

        # 记录删除操作（可选：保存到日志文件）
        deleted_user_info = {
            'username': username,
            'deleted_at': beijing_now_iso(),
            'deleted_by': admin_username,
            'user_data': self.users[username].copy()  # 备份用户数据
        }

        # 删除用户
        del self.users[username]

        if self.save_users():
            # 可以选择将删除记录保存到单独的日志文件
            self._log_user_deletion(deleted_user_info)
            return True, f"用户 {username} 已删除"
        else:
            return False, "删除用户失败"

    def _log_user_deletion(self, deletion_info):
        """记录用户删除日志"""
        try:
            log_file = 'deleted_users.json'
            deleted_users = []

            # 读取现有的删除记录
            if os.path.exists(log_file):
                try:
                    with open(log_file, 'r', encoding='utf-8') as f:
                        deleted_users = json.load(f)
                except (json.JSONDecodeError, IOError):
                    deleted_users = []

            # 添加新的删除记录
            deleted_users.append(deletion_info)

            # 保存删除记录
            with open(log_file, 'w', encoding='utf-8') as f:
                json.dump(deleted_users, f, ensure_ascii=False, indent=2)
        except Exception as e:
            # 记录日志失败不影响删除操作
            print(f"记录删除日志失败: {e}")

# 装饰器：要求用户登录
def login_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'username' not in session:
            if request.is_json:
                return jsonify({'success': False, 'message': '请先登录'}), 401
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    return decorated_function

# 装饰器：要求管理员权限
def admin_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'username' not in session:
            if request.is_json:
                return jsonify({'success': False, 'message': '请先登录'}), 401
            return redirect(url_for('login'))
        
        # 这里需要在app.py中初始化user_manager后才能使用
        # 暂时简化处理
        return f(*args, **kwargs)
    return decorated_function

# 装饰器：检查积分是否足够
def points_required(points_needed=1):
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if 'username' not in session:
                if request.is_json:
                    return jsonify({'success': False, 'message': '请先登录'}), 401
                return redirect(url_for('login'))
            
            # 这里需要在具体使用时检查积分
            return f(*args, **kwargs)
        return decorated_function
    return decorator
